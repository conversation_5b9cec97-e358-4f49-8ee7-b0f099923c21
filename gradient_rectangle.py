import matplotlib.pyplot as plt
import numpy as np
from matplotlib.transforms import Affine2D
from matplotlib.patches import Polygon
from matplotlib.colors import LinearSegmentedColormap

def create_gradient_rectangle():
    """
    创建一个16:9比例的矩形，具有径向渐变效果（中心深，周围浅），
    并在中间高度位置画一条横线
    """
    # 设置图形大小，保持16:9比例
    fig_width = 16
    fig_height = fig_width * 9 / 16
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    
    # 矩形的尺寸和位置
    rect_width = 10  # 减小宽度以适应旋转后的窗口
    rect_height = rect_width * 9 / 16  # 保持16:9比例
    rect_left = fig_width / 2 - rect_width / 2  # 在窗口中心
    rect_bottom = fig_height / 2 - rect_height / 2  # 在窗口中心
    
    # 计算矩形中心点
    center_x = rect_left + rect_width / 2
    center_y = rect_bottom + rect_height / 2

    # 旋转角度（顺时针10度，需要转换为弧度并取负值）
    rotation_angle_degree = -10  # 负值表示顺时针
    rotation_angle_radian = rotation_angle_degree * np.pi / 180

    # 手动计算矩形的四个顶点坐标（旋转前）
    # 矩形的四个顶点（相对于中心点的坐标）
    vertices_relative = np.array([
        [-rect_width/2, -rect_height/2],  # 左下角
        [rect_width/2, -rect_height/2],   # 右下角
        [rect_width/2, rect_height/2],    # 右上角
        [-rect_width/2, rect_height/2]    # 左上角
    ])

    # 应用旋转变换
    cos_angle = np.cos(rotation_angle_radian)
    sin_angle = np.sin(rotation_angle_radian)
    rotation_matrix = np.array([
        [cos_angle, -sin_angle],
        [sin_angle, cos_angle]
    ])

    # 旋转顶点
    vertices_rotated = vertices_relative @ rotation_matrix.T

    # 平移到最终位置
    vertices_final = vertices_rotated + np.array([center_x, center_y])

    # 创建渐变填充
    # 在矩形内部创建径向渐变效果
    n_layers = 50  # 渐变层数
    max_distance = np.sqrt((rect_width/2)**2 + (rect_height/2)**2)

    # 从外到内绘制渐变层
    for i in range(n_layers):
        # 计算当前层的缩放比例（从1到0）
        scale = 1 - (i / n_layers)
        if scale <= 0:
            break

        # 缩放顶点（相对于中心）
        scaled_vertices_relative = vertices_relative * scale
        scaled_vertices_rotated = scaled_vertices_relative @ rotation_matrix.T
        scaled_vertices_final = scaled_vertices_rotated + np.array([center_x, center_y])

        # 计算颜色强度（中心深，边缘浅）
        color_intensity = 1 - scale  # 0到1，中心为1（深蓝），边缘为0（浅蓝）
        color = plt.cm.Blues_r(color_intensity)

        # 绘制填充多边形
        polygon = Polygon(scaled_vertices_final, facecolor=color, edgecolor='none', alpha=0.8/n_layers*2)
        ax.add_patch(polygon)

    # 绘制矩形边框（连接四个顶点）
    # 添加第一个顶点到末尾以闭合矩形
    vertices_closed = np.vstack([vertices_final, vertices_final[0]])
    ax.plot(vertices_closed[:, 0], vertices_closed[:, 1],
            color='black', linewidth=2, alpha=1.0)

    # 在矩形中间高度位置画一条横线（旋转）
    # 横线在矩形坐标系中的位置（矩形中心的水平线）
    line_start_x, line_end_x = rect_left, rect_left + rect_width
    line_y = center_y  # 矩形中心的y坐标

    # 计算旋转后的线条端点（使用与矩形相同的旋转变换）
    # 起点（矩形左边中点）
    start_x_rot = center_x + (line_start_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    start_y_rot = center_y + (line_start_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)
    # 终点（矩形右边中点）
    end_x_rot = center_x + (line_end_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    end_y_rot = center_y + (line_end_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)

    # 计算垂直分割线的端点（矩形上下边的中点）
    vertical_line_start_relative = np.array([0, -rect_height/2])   # 下边中点
    vertical_line_end_relative = np.array([0, rect_height/2])      # 上边中点

    # 应用旋转变换到垂直分割线端点
    vertical_line_start_rotated = vertical_line_start_relative @ rotation_matrix.T
    vertical_line_end_rotated = vertical_line_end_relative @ rotation_matrix.T

    # 平移到最终位置
    vertical_line_start_final = vertical_line_start_rotated + np.array([center_x, center_y])
    vertical_line_end_final = vertical_line_end_rotated + np.array([center_x, center_y])

    # 矩形中心点（两条线的交点）
    center_point = np.array([center_x, center_y])

    # 创建两个四边形（左右分割）
    # 左半部分四边形：左下角 -> 左上角 -> 上边中点 -> 下边中点
    left_quad_vertices = np.array([
        vertices_final[0],           # 左下角
        vertices_final[3],           # 左上角
        vertical_line_end_final,     # 上边中点
        vertical_line_start_final    # 下边中点
    ])

    # 右半部分四边形：下边中点 -> 上边中点 -> 右上角 -> 右下角
    right_quad_vertices = np.array([
        vertical_line_start_final,   # 下边中点
        vertical_line_end_final,     # 上边中点
        vertices_final[2],           # 右上角
        vertices_final[1]            # 右下角
    ])

    # 绘制两个四边形的边框
    # 左半部分四边形
    left_quad_closed = np.vstack([left_quad_vertices, left_quad_vertices[0]])
    ax.plot(left_quad_closed[:, 0], left_quad_closed[:, 1],
            color='blue', linewidth=2, alpha=0.8, label='左半部分')

    # 右半部分四边形
    right_quad_closed = np.vstack([right_quad_vertices, right_quad_vertices[0]])
    ax.plot(right_quad_closed[:, 0], right_quad_closed[:, 1],
            color='green', linewidth=2, alpha=0.8, label='右半部分')

    # 绘制水平线（分成两段）
    # 左段：从左边中点到中心
    ax.plot([start_x_rot, center_point[0]],
            [start_y_rot, center_point[1]],
            color='red', linewidth=3, alpha=0.8, label='水平线-左段')

    # 右段：从中心到右边中点
    ax.plot([center_point[0], end_x_rot],
            [center_point[1], end_y_rot],
            color='orange', linewidth=3, alpha=0.8, label='水平线-右段')

    # 绘制垂直分割线
    ax.plot([vertical_line_start_final[0], vertical_line_end_final[0]],
            [vertical_line_start_final[1], vertical_line_end_final[1]],
            color='purple', linewidth=3, alpha=0.8, label='垂直分割线')

    # 设置坐标轴（扩大范围以容纳旋转后的矩形）
    ax.set_xlim(0, 16)
    ax.set_ylim(0, 9)
    ax.set_aspect('equal')
    
    # 添加标题和标签
    ax.set_title('Frame Layout', fontsize=16, pad=20)
    ax.set_xlabel('宽度', fontsize=12)
    ax.set_ylabel('高度', fontsize=12)
    
    # 添加网格
    ax.grid(True, alpha=0.3)

    # 添加图例
    ax.legend(loc='upper right', bbox_to_anchor=(1, 1))

    # 显示图形
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("绘制Frame Layout...")
    create_gradient_rectangle()
