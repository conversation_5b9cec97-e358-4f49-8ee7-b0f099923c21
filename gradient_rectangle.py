import matplotlib.pyplot as plt
import numpy as np
from matplotlib.transforms import Affine2D
from matplotlib.patches import Polygon

def create_gradient_rectangle():
    """
    创建一个16:9比例的矩形，具有径向渐变效果（中心深，周围浅），
    并在中间高度位置画一条横线
    """
    # 设置图形大小，保持16:9比例
    fig_width = 16
    fig_height = fig_width * 9 / 16
    fig, ax = plt.subplots(figsize=(fig_width, fig_height))
    
    # 矩形的尺寸和位置
    rect_width = 10  # 减小宽度以适应旋转后的窗口
    rect_height = rect_width * 9 / 16  # 保持16:9比例
    rect_left = fig_width / 2 - rect_width / 2  # 在窗口中心
    rect_bottom = fig_height / 2 - rect_height / 2  # 在窗口中心
    
    # 计算矩形中心点
    center_x = rect_left + rect_width / 2
    center_y = rect_bottom + rect_height / 2

    # 旋转角度（顺时针10度，需要转换为弧度并取负值）
    rotation_angle_degree = -10  # 负值表示顺时针
    rotation_angle_radian = rotation_angle_degree * np.pi / 180

    # 手动计算矩形的四个顶点坐标（旋转前）
    # 矩形的四个顶点（相对于中心点的坐标）
    vertices_relative = np.array([
        [-rect_width/2, -rect_height/2],  # 左下角
        [rect_width/2, -rect_height/2],   # 右下角
        [rect_width/2, rect_height/2],    # 右上角
        [-rect_width/2, rect_height/2]    # 左上角
    ])

    # 应用旋转变换
    cos_angle = np.cos(rotation_angle_radian)
    sin_angle = np.sin(rotation_angle_radian)
    rotation_matrix = np.array([
        [cos_angle, -sin_angle],
        [sin_angle, cos_angle]
    ])

    # 旋转顶点
    vertices_rotated = vertices_relative @ rotation_matrix.T

    # 平移到最终位置
    vertices_final = vertices_rotated + np.array([center_x, center_y])



    # 绘制矩形边框（连接四个顶点）
    # 添加第一个顶点到末尾以闭合矩形
    # vertices_closed = np.vstack([vertices_final, vertices_final[0]])
    # ax.plot(vertices_closed[:, 0], vertices_closed[:, 1],
    #         color='black', linewidth=2, alpha=1.0)

    # 在矩形中间高度位置画一条横线（旋转）
    # 横线在矩形坐标系中的位置（矩形中心的水平线）
    line_start_x, line_end_x = rect_left, rect_left + rect_width
    line_y = center_y  # 矩形中心的y坐标

    # 计算旋转后的线条端点（使用与矩形相同的旋转变换）
    # 起点（矩形左边中点）
    start_x_rot = center_x + (line_start_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    start_y_rot = center_y + (line_start_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)
    # 终点（矩形右边中点）
    end_x_rot = center_x + (line_end_x - center_x) * np.cos(rotation_angle_radian) - (line_y - center_y) * np.sin(rotation_angle_radian)
    end_y_rot = center_y + (line_end_x - center_x) * np.sin(rotation_angle_radian) + (line_y - center_y) * np.cos(rotation_angle_radian)

    # 计算垂直分割线（平行于y轴，不旋转）
    # 这条线穿过旋转后矩形的中心，平行于y轴
    vertical_line_x = center_x  # x坐标固定为矩形中心的x坐标
    vertical_line_start_y = center_y - 6  # 向下延伸
    vertical_line_end_y = center_y + 6    # 向上延伸

    vertical_line_start_final = np.array([vertical_line_x, vertical_line_start_y])
    vertical_line_end_final = np.array([vertical_line_x, vertical_line_end_y])

    # 计算垂直分割线与矩形边界的交点
    def line_intersection(p1, p2, x_vertical):
        """计算垂直线 x=x_vertical 与线段 p1-p2 的交点"""
        if abs(p2[0] - p1[0]) < 1e-10:  # 线段也是垂直的
            return None

        # 计算交点的y坐标
        t = (x_vertical - p1[0]) / (p2[0] - p1[0])
        if 0 <= t <= 1:  # 交点在线段上
            y = p1[1] + t * (p2[1] - p1[1])
            return np.array([x_vertical, y])
        return None

    # 找到垂直线与矩形各边的交点
    intersections = []
    edges = [
        (vertices_final[0], vertices_final[1]),  # 下边
        (vertices_final[1], vertices_final[2]),  # 右边
        (vertices_final[2], vertices_final[3]),  # 上边
        (vertices_final[3], vertices_final[0])   # 左边
    ]

    for edge in edges:
        intersection = line_intersection(edge[0], edge[1], vertical_line_x)
        if intersection is not None:
            intersections.append(intersection)

    # 应该有两个交点，按y坐标排序
    intersections = sorted(intersections, key=lambda p: p[1])

    if len(intersections) >= 2:
        bottom_intersection = intersections[0]  # 下交点
        top_intersection = intersections[-1]   # 上交点

        # 创建两个四边形（左右分割）
        # 需要确定哪些顶点在分割线的左边和右边
        left_vertices = []
        right_vertices = []

        for vertex in vertices_final:
            if vertex[0] < vertical_line_x:
                left_vertices.append(vertex)
            else:
                right_vertices.append(vertex)

        # 按逆时针顺序排列顶点
        # 左半部分：包含左边的顶点和两个交点
        left_quad_vertices = left_vertices + [top_intersection, bottom_intersection]
        # 按角度排序以确保逆时针顺序
        center_left = np.mean(left_quad_vertices, axis=0)
        angles_left = [np.arctan2(v[1] - center_left[1], v[0] - center_left[0]) for v in left_quad_vertices]
        sorted_indices_left = np.argsort(angles_left)
        left_quad_vertices = [left_quad_vertices[i] for i in sorted_indices_left]

        # 右半部分：包含右边的顶点和两个交点
        right_quad_vertices = right_vertices + [bottom_intersection, top_intersection]
        # 按角度排序以确保逆时针顺序
        center_right = np.mean(right_quad_vertices, axis=0)
        angles_right = [np.arctan2(v[1] - center_right[1], v[0] - center_right[0]) for v in right_quad_vertices]
        sorted_indices_right = np.argsort(angles_right)
        right_quad_vertices = [right_quad_vertices[i] for i in sorted_indices_right]

        left_quad_vertices = np.array(left_quad_vertices)
        right_quad_vertices = np.array(right_quad_vertices)

        # 计算平移量：使右侧四边形的最高点与左侧四边形的最高点对齐
        left_max_y = np.max(left_quad_vertices[:, 1])
        right_max_y = np.max(right_quad_vertices[:, 1])
        vertical_offset = left_max_y - right_max_y

        # 平移右侧四边形
        right_quad_vertices[:, 1] += vertical_offset

        print(f"右侧四边形向上平移距离: {vertical_offset:.3f} 单位长度")



    else:
        # 如果没有找到交点，使用默认的四边形
        left_quad_vertices = vertices_final[:2]
        right_quad_vertices = vertices_final[2:]
        vertical_offset = 0

    # 计算压缩比例（移到这里，在绘制之前）
    # 合并两个四边形的所有顶点
    all_vertices = np.vstack([left_quad_vertices, right_quad_vertices])

    # 找到边界框
    min_x = np.min(all_vertices[:, 0])
    max_x = np.max(all_vertices[:, 0])
    min_y = np.min(all_vertices[:, 1])
    max_y = np.max(all_vertices[:, 1])

    # 计算当前边界框的宽度和高度
    bbox_width = max_x - min_x
    bbox_height = max_y - min_y

    # 目标宽高比 16:9
    target_ratio = 16.0 / 9.0
    current_ratio = bbox_width / bbox_height

    # 根据16:9比例调整矩形尺寸
    if current_ratio > target_ratio:
        # 当前太宽，需要增加高度
        new_width = bbox_width
        new_height = bbox_width / target_ratio
    else:
        # 当前太高，需要增加宽度
        new_height = bbox_height
        new_width = bbox_height * target_ratio

    # 计算矩形中心（保持在边界框中心）
    center_x_bbox = (min_x + max_x) / 2
    center_y_bbox = (min_y + max_y) / 2

    # 计算轴对齐矩形的边界
    rect_min_x = center_x_bbox - new_width / 2
    rect_max_x = center_x_bbox + new_width / 2
    rect_min_y = center_y_bbox - new_height / 2
    rect_max_y = center_y_bbox + new_height / 2

    # 定义矩形中心
    rect_center = np.array([center_x_bbox, center_y_bbox])

    # 计算不均匀网格的最小间距
    def generate_nonuniform_grid_lines(center, min_val, max_val, num_lines=8):
        """生成非均匀分布的网格线位置"""
        half_lines = num_lines // 2
        t = np.linspace(0, 1, half_lines + 1)[1:]
        nonlinear_t = t ** 1.5
        max_distance_positive = max_val - center
        max_distance_negative = center - min_val
        positive_lines = center + nonlinear_t * max_distance_positive
        negative_lines = center - nonlinear_t * max_distance_negative
        all_lines = np.concatenate([negative_lines[::-1], positive_lines])
        return all_lines

    # 生成原始不均匀网格来计算最小间距
    vertical_lines_orig = generate_nonuniform_grid_lines(rect_center[0], rect_min_x, rect_max_x, num_lines=60)
    horizontal_lines_orig = generate_nonuniform_grid_lines(rect_center[1], rect_min_y, rect_max_y, num_lines=35)

    # 计算最小网格间距
    min_vertical_spacing = np.min(np.diff(vertical_lines_orig))
    min_horizontal_spacing = np.min(np.diff(horizontal_lines_orig))
    min_grid_spacing = min(min_vertical_spacing, min_horizontal_spacing)

    print(f"最小网格间距: {min_grid_spacing:.4f}")

    # 计算压缩比例
    original_width = rect_max_x - rect_min_x
    original_height = rect_max_y - rect_min_y

    # 计算新的尺寸（基于均匀的最小网格）
    new_width_uniform = int(original_width / min_grid_spacing) * min_grid_spacing
    new_height_uniform = int(original_height / min_grid_spacing) * min_grid_spacing

    # 计算压缩比例
    scale_x = new_width_uniform / original_width
    scale_y = new_height_uniform / original_height
    scale_factor = min(scale_x, scale_y)  # 使用较小的比例保持比例一致

    print(f"压缩比例: {scale_factor:.4f}")

    # 应用压缩到所有几何元素
    # 压缩四边形顶点
    left_quad_vertices_scaled = left_quad_vertices * scale_factor
    right_quad_vertices_scaled = right_quad_vertices * scale_factor

    # 重新计算压缩后的边界框
    all_vertices_scaled = np.vstack([left_quad_vertices_scaled, right_quad_vertices_scaled])
    rect_min_x_scaled = np.min(all_vertices_scaled[:, 0])
    rect_max_x_scaled = np.max(all_vertices_scaled[:, 0])
    rect_min_y_scaled = np.min(all_vertices_scaled[:, 1])
    rect_max_y_scaled = np.max(all_vertices_scaled[:, 1])

    # 生成均匀网格
    grid_spacing = min_grid_spacing * scale_factor

    # 扩展网格范围以覆盖整个图形
    grid_min_x = rect_min_x_scaled - grid_spacing
    grid_max_x = rect_max_x_scaled + grid_spacing
    grid_min_y = rect_min_y_scaled - grid_spacing
    grid_max_y = rect_max_y_scaled + grid_spacing

    # 生成均匀的垂直和水平网格线
    vertical_lines = np.arange(grid_min_x, grid_max_x + grid_spacing, grid_spacing)
    horizontal_lines = np.arange(grid_min_y, grid_max_y + grid_spacing, grid_spacing)

    # 绘制均匀网格线
    for x in vertical_lines:
        ax.plot([x, x], [grid_min_y, grid_max_y],
               color='gray', linewidth=0.8, alpha=0.6)

    for y in horizontal_lines:
        ax.plot([grid_min_x, grid_max_x], [y, y],
               color='gray', linewidth=0.8, alpha=0.6)

    # 绘制两个四边形的边框（使用压缩后的坐标）
    # 左半部分四边形
    left_polygon = Polygon(left_quad_vertices_scaled, facecolor='none', edgecolor='black', linewidth=2, alpha=0.8)
    ax.add_patch(left_polygon)

    # 右半部分四边形
    right_polygon = Polygon(right_quad_vertices_scaled, facecolor='none', edgecolor='black', linewidth=2, alpha=0.8)
    ax.add_patch(right_polygon)

    # 应用压缩到水平线坐标
    start_x_rot_scaled = start_x_rot * scale_factor
    start_y_rot_scaled = start_y_rot * scale_factor
    end_x_rot_scaled = end_x_rot * scale_factor
    end_y_rot_scaled = end_y_rot * scale_factor
    vertical_line_x_scaled = vertical_line_x * scale_factor

    # 计算垂直分割线与水平线的交点（使用压缩后的坐标）
    horizontal_intersection = line_intersection(
        np.array([start_x_rot_scaled, start_y_rot_scaled]),
        np.array([end_x_rot_scaled, end_y_rot_scaled]),
        vertical_line_x_scaled
    )

    if horizontal_intersection is not None:
        # 绘制水平线（分成两段，但颜色相同）
        # 左段：从左边中点到交点
        ax.plot([start_x_rot_scaled, horizontal_intersection[0]],
                [start_y_rot_scaled, horizontal_intersection[1]],
                color='red', linewidth=3, alpha=0.8)

        # 右段：从交点到右边中点（需要平移）
        right_segment_start_y = horizontal_intersection[1] + vertical_offset * scale_factor
        right_segment_end_y = end_y_rot_scaled + vertical_offset * scale_factor
        ax.plot([horizontal_intersection[0], end_x_rot_scaled],
                [right_segment_start_y, right_segment_end_y],
                color='red', linewidth=3, alpha=0.8)
    else:
        # 如果没有交点，绘制完整的水平线
        ax.plot([start_x_rot_scaled, end_x_rot_scaled], [start_y_rot_scaled, end_y_rot_scaled],
                color='red', linewidth=3, alpha=0.8)

    # 绘制垂直分割线
    # ax.plot([vertical_line_start_final[0], vertical_line_end_final[0]],
    #         [vertical_line_start_final[1], vertical_line_end_final[1]],
    #         color='purple', linewidth=3, alpha=0.8, label='Vertical Split Line')

    # 为图例添加水平线标签（只需要一个）
    # ax.plot([], [], color='red', linewidth=3, alpha=0.8, label='Horizontal Line')

    # 重复的代码已经移到前面，这里删除

    # 注释掉渐变填充代码
    # # 为轴对齐矩形创建渐变填充
    # # 矩形的四个顶点
    # rect_vertices = np.array([
    #     [rect_min_x, rect_min_y],  # 左下
    #     [rect_max_x, rect_min_y],  # 右下
    #     [rect_max_x, rect_max_y],  # 右上
    #     [rect_min_x, rect_max_y]   # 左上
    # ])
    #
    # # 矩形中心
    # rect_center = np.array([center_x_bbox, center_y_bbox])
    #
    # # 创建基于距离的径向渐变效果
    # # 使用imshow创建真正的径向渐变
    #
    # # 创建网格
    # x_grid = np.linspace(rect_min_x, rect_max_x, 200)
    # y_grid = np.linspace(rect_min_y, rect_max_y, 200)
    # X, Y = np.meshgrid(x_grid, y_grid)
    #
    # # 计算每个点到中心的距离
    # distances = np.sqrt((X - rect_center[0])**2 + (Y - rect_center[1])**2)
    #
    # # 计算矩形的最大距离（从中心到角落）
    # max_distance = np.sqrt((new_width/2)**2 + (new_height/2)**2)
    #
    # # 归一化距离（0到1）
    # normalized_distances = distances / max_distance
    #
    # # 创建颜色强度（距离越近颜色越深）
    # color_intensity = 1 - normalized_distances  # 中心为1，边缘为0
    # color_intensity = np.clip(color_intensity, 0, 1)  # 确保在0-1范围内
    #
    # # 应用颜色映射（调整为更浅的颜色范围）
    # colors = plt.cm.RdPu(0.05 + 0.5 * color_intensity)
    #
    # # 创建遮罩，只在矩形内部显示渐变
    # mask = (X >= rect_min_x) & (X <= rect_max_x) & (Y >= rect_min_y) & (Y <= rect_max_y)
    # colors[..., 3] = mask.astype(float) * 0.8  # 设置透明度
    #
    # # 绘制渐变
    # ax.imshow(colors, extent=[rect_min_x, rect_max_x, rect_min_y, rect_max_y],
    #           origin='lower', aspect='auto', interpolation='bilinear')

    # 计算当前不均匀网格的最小间距
    def generate_nonuniform_grid_lines(center, min_val, max_val, num_lines=8):
        """生成非均匀分布的网格线位置"""
        half_lines = num_lines // 2
        t = np.linspace(0, 1, half_lines + 1)[1:]
        nonlinear_t = t ** 1.5
        max_distance_positive = max_val - center
        max_distance_negative = center - min_val
        positive_lines = center + nonlinear_t * max_distance_positive
        negative_lines = center - nonlinear_t * max_distance_negative
        all_lines = np.concatenate([negative_lines[::-1], positive_lines])
        return all_lines

    # 定义矩形中心
    rect_center = np.array([center_x_bbox, center_y_bbox])

    # 生成原始不均匀网格来计算最小间距
    vertical_lines_orig = generate_nonuniform_grid_lines(rect_center[0], rect_min_x, rect_max_x, num_lines=60)
    horizontal_lines_orig = generate_nonuniform_grid_lines(rect_center[1], rect_min_y, rect_max_y, num_lines=35)

    # 计算最小网格间距
    min_vertical_spacing = np.min(np.diff(vertical_lines_orig))
    min_horizontal_spacing = np.min(np.diff(horizontal_lines_orig))
    min_grid_spacing = min(min_vertical_spacing, min_horizontal_spacing)

    print(f"最小网格间距: {min_grid_spacing:.4f}")

    # 计算压缩比例
    original_width = rect_max_x - rect_min_x
    original_height = rect_max_y - rect_min_y

    # 计算新的尺寸（基于均匀的最小网格）
    new_width = int(original_width / min_grid_spacing) * min_grid_spacing
    new_height = int(original_height / min_grid_spacing) * min_grid_spacing

    # 计算压缩比例
    scale_x = new_width / original_width
    scale_y = new_height / original_height
    scale_factor = min(scale_x, scale_y)  # 使用较小的比例保持比例一致

    print(f"压缩比例: {scale_factor:.4f}")

    # 应用压缩到所有几何元素
    # 压缩四边形顶点
    left_quad_vertices_scaled = left_quad_vertices * scale_factor
    right_quad_vertices_scaled = right_quad_vertices * scale_factor

    # 重新计算压缩后的边界框
    all_vertices_scaled = np.vstack([left_quad_vertices_scaled, right_quad_vertices_scaled])
    rect_min_x_scaled = np.min(all_vertices_scaled[:, 0])
    rect_max_x_scaled = np.max(all_vertices_scaled[:, 0])
    rect_min_y_scaled = np.min(all_vertices_scaled[:, 1])
    rect_max_y_scaled = np.max(all_vertices_scaled[:, 1])

    # 生成均匀网格
    grid_spacing = min_grid_spacing * scale_factor

    # 扩展网格范围以覆盖整个图形
    grid_min_x = rect_min_x_scaled - grid_spacing
    grid_max_x = rect_max_x_scaled + grid_spacing
    grid_min_y = rect_min_y_scaled - grid_spacing
    grid_max_y = rect_max_y_scaled + grid_spacing

    # 生成均匀的垂直和水平网格线
    vertical_lines = np.arange(grid_min_x, grid_max_x + grid_spacing, grid_spacing)
    horizontal_lines = np.arange(grid_min_y, grid_max_y + grid_spacing, grid_spacing)

    # 绘制均匀网格线
    for x in vertical_lines:
        ax.plot([x, x], [grid_min_y, grid_max_y],
               color='gray', linewidth=0.8, alpha=0.6)

    for y in horizontal_lines:
        ax.plot([grid_min_x, grid_max_x], [y, y],
               color='gray', linewidth=0.8, alpha=0.6)

    # 绘制压缩后的轴对齐矩形边框
    scaled_width = rect_max_x_scaled - rect_min_x_scaled
    scaled_height = rect_max_y_scaled - rect_min_y_scaled
    axis_aligned_rect = plt.Rectangle((rect_min_x_scaled, rect_min_y_scaled), scaled_width, scaled_height,
                                     fill=False, edgecolor='orange', linewidth=3, alpha=0.8,
                                     label='Axis-Aligned Bounding Rect (Scaled)')
    ax.add_patch(axis_aligned_rect)

    # 计算尺寸对比
    original_area = rect_width * rect_height
    axis_aligned_area = new_width * new_height
    area_ratio = axis_aligned_area / original_area

    print(f"原矩形尺寸: {rect_width} x {rect_height}")
    print(f"原矩形面积: {original_area}")
    print(f"轴对齐矩形尺寸: {new_width:.2f} x {new_height:.2f}")
    print(f"轴对齐矩形面积: {axis_aligned_area:.2f}")
    print(f"面积比例: {area_ratio:.2f} (轴对齐矩形是原矩形的 {area_ratio:.2f} 倍)")
    print(f"面积增加: {(area_ratio - 1) * 100:.1f}%")

    # 设置坐标轴（适应压缩后的图形和网格）
    margin = grid_spacing * 2  # 添加一些边距
    ax.set_xlim(grid_min_x - margin, grid_max_x + margin)
    ax.set_ylim(grid_min_y - margin, grid_max_y + margin)
    ax.set_aspect('equal')
    
    # 添加标题和标签
    ax.set_title('Frame Layout', fontsize=16, pad=20)
    ax.set_xlabel('Width', fontsize=12)
    ax.set_ylabel('Height', fontsize=12)

    # 添加网格
    # ax.grid(True, alpha=0.3)

    # 添加图例
    # ax.legend(loc='upper right', bbox_to_anchor=(1, 1))

    # 显示图形
    plt.tight_layout()
    plt.show()

if __name__ == "__main__":
    print("绘制Frame Layout...")
    create_gradient_rectangle()
